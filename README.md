# Hibernet
A powerful HTTP flooding tool.
You can choose if use proxy/socks to anonymize attack.

Please suggest me some ideas, and report bugs!
Thanks!


<h2>Install dependencies</h2>
To use it you have to use python3 and you also need an extra module.

To install it if you are running linux or mac, type:
<pre>pip3 install pysocks</pre>

If you are under winzoz, type:
<pre>py -m pip install pysocks</pre>

<h2>Usage</h2>
Just type on a terminal:
<pre>python3 HibernetV3.x</pre>

Or double click on the program in winzoz.


<h2>New functionality for multi target attack.</h2>
Just put your IPs or urls inside "ips.txt"

<h2>Proxy generator</h2>
If you want more proxies for your attacks, you can use HiberProxy or HiberSOCKS!

You can found it here: https://github.com/All3xJ/HiberProxy and here: https://github.com/All3xJ/HiberSOCKS


<h1>ENJOY!</h1>



![alt text](https://i.imgur.com/odr1rPd.png)
![alt text](https://i.imgur.com/3YNngR0.png)
![alt text](https://i.imgur.com/BcvW4C3.png)


Attack from just 1 lousy vps can be dangerous for a target, so don't aim targets that are not your property.



<h2>Demonstration video:</h2>
https://www.youtube.com/watch?v=G84R0qKMpO8
